import React, { useState, useEffect } from "react";
import { Calendar } from "./calendar";
import { TimeRangePicker } from "./time-picker";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import { Button } from "./button";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Clock, X } from "lucide-react";
import { 
  formatDateForAPI, 
  formatTimeForAPI, 
  isValidTimeRange, 
  isPastDate,
  getCurrentDate 
} from "@/utils/dateTimeUtils";

interface DateTimeFilterProps {
  selectedDate: Date | undefined;
  startTime: string;
  endTime: string;
  onDateChange: (date: Date | undefined) => void;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
  onClear: () => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
}

export function DateTimeFilter({
  selectedDate,
  startTime,
  endTime,
  onDateChange,
  onStartTimeChange,
  onEndTimeChange,
  onClear,
  disabled = false,
  className,
  placeholder = "When?"
}: DateTimeFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  // Show time picker when a date is selected
  useEffect(() => {
    setShowTimePicker(!!selectedDate);
  }, [selectedDate]);

  const handleDateSelect = (date: Date | undefined) => {
    onDateChange(date);
    if (!date) {
      // Clear times when date is cleared
      onStartTimeChange("");
      onEndTimeChange("");
      setShowTimePicker(false);
    }
  };

  const handleClear = () => {
    onDateChange(undefined);
    onStartTimeChange("");
    onEndTimeChange("");
    setShowTimePicker(false);
    onClear();
    setIsOpen(false);
  };

  const hasSelection = selectedDate || startTime || endTime;
  const isValidSelection = selectedDate && (!startTime || !endTime || isValidTimeRange(startTime, endTime));

  // Format display text
  const getDisplayText = () => {
    if (!selectedDate) return placeholder;
    
    let text = format(selectedDate, "MMM dd, yyyy");
    
    if (startTime && endTime) {
      text += ` • ${startTime} - ${endTime}`;
    } else if (startTime) {
      text += ` • from ${startTime}`;
    } else if (endTime) {
      text += ` • until ${endTime}`;
    }
    
    return text;
  };

  return (
    <div className={cn("relative", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "h-8 px-3 text-sm border-none focus-visible:ring-0 shadow-none justify-start min-w-[120px] max-w-[200px]",
              !hasSelection && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-3 w-3 flex-shrink-0" />
            <span className="truncate">{getDisplayText()}</span>
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col">
            {/* Calendar Section */}
            <div className="p-3 border-b">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={(date) => isPastDate(date)}
                initialFocus
                className="rounded-md"
              />
            </div>
            
            {/* Time Picker Section - Only show when date is selected */}
            {showTimePicker && selectedDate && (
              <div className="p-3 border-b">
                <div className="flex items-center gap-2 mb-3">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Select Time Range (Optional)</span>
                </div>
                
                <TimeRangePicker
                  startTime={startTime}
                  endTime={endTime}
                  onStartTimeChange={onStartTimeChange}
                  onEndTimeChange={onEndTimeChange}
                  disabled={disabled}
                  className="justify-center"
                />
                
                {startTime && endTime && !isValidTimeRange(startTime, endTime) && (
                  <div className="text-xs text-red-500 mt-2 text-center">
                    End time must be after start time
                  </div>
                )}
              </div>
            )}
            
            {/* Action Buttons */}
            <div className="p-3 flex justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
                disabled={!hasSelection}
                className="text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
              
              <Button
                size="sm"
                onClick={() => setIsOpen(false)}
                disabled={!isValidSelection}
                className="text-xs"
              >
                Done
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Clear button when there's a selection */}
      {hasSelection && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute -right-6 top-0 h-8 w-6 text-gray-400 hover:text-gray-600"
          onClick={handleClear}
          disabled={disabled}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}
