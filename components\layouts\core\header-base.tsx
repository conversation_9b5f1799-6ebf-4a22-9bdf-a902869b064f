"use client";

import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { useCartContext } from "@/context/hooks/use-cart-hook";
import { useNotificationContext } from "@/context/hooks/use-notification-hook";
import { handleRoleChange, useLogout } from "@/hooks/authHooks";
import { useBoolean } from "@/hooks/use-boolean";
import { paths } from "@/routes/paths";
import { getInitials } from "@/utils/getInitials";
import Image from "next/image";
import Link from "next/link";
import { NotificationPopover } from "@/components/notification/NotificationPopover";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { usePathname } from "next/navigation";
import {
  MessageSquare,
  X,
  Menu,
  Search,
  User,
  Settings,
  Home,
  ShoppingBag,
  Clock,
  MapPin,
} from "lucide-react";
import ChatBadge from "@/components/chat/ChatBadge";

interface HeaderBaseProps {
  hamburgerMenu: {
    value: boolean;
    onTrue: () => void;
    onFalse: () => void;
    onToggle: () => void;
  };
}

export const HeaderBase: React.FC<HeaderBaseProps> = ({ hamburgerMenu }) => {
  const userMenu = useBoolean(false);
  const mobileMenu = useBoolean(false);
  const { user, host, switchRole, authenticated } = useAuthContext();
  const { cartItems } = useCartContext();
  useNotificationContext(); // Initialize notification context
  const { logout } = useLogout();
  const router = useRouter();
  const pathname = usePathname();

  // Close mobile menu when route changes
  useEffect(() => {
    mobileMenu.onFalse();
  }, [pathname]);

  // Close menus when clicking outside and handle keyboard navigation
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (
        !target.closest("[data-mobile-menu]") &&
        !target.closest("[data-mobile-menu-button]")
      ) {
        mobileMenu.onFalse();
      }
      if (
        !target.closest("[data-user-menu]") &&
        !target.closest("[data-user-menu-button]")
      ) {
        userMenu.onFalse();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        if (mobileMenu.value) {
          mobileMenu.onFalse();
        }
        if (userMenu.value) {
          userMenu.onFalse();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [mobileMenu.value, userMenu.value]);

  // Focus management for mobile menu
  useEffect(() => {
    if (mobileMenu.value) {
      // Trap focus within mobile menu
      const mobileMenuElement = document.querySelector("[data-mobile-menu]");
      const focusableElements = mobileMenuElement?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[
          focusableElements.length - 1
        ] as HTMLElement;

        // Focus first element
        firstElement.focus();

        const handleTabKey = (e: KeyboardEvent) => {
          if (e.key === "Tab") {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
              }
            }
          }
        };

        document.addEventListener("keydown", handleTabKey);

        return () => {
          document.removeEventListener("keydown", handleTabKey);
        };
      }
    }
  }, [mobileMenu.value]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenu.value) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [mobileMenu.value]);

  // Define menu items for user and host
  const userMenuItems = [
    { label: "My Orders", path: "/orders" },
    { label: "Account", path: paths.user.account },
    { label: "Payments", path: paths.user.payment },
    { label: "Requests", path: paths.user.requests },
    { label: "Login & Security", path: paths.user.loginSecurity },
  ];

  const hostMenuItems = [
    { label: "Settings", path: paths.host.profile.settings },
  ];

  const menuItems = user?.email ? userMenuItems : hostMenuItems;

  // Navigation links for host mode
  const hostNavLinks = [
    { label: "About", path: paths?.host?.profile?.about },
    { label: "Menu", path: paths?.host?.profile?.dishes },
    { label: "Locations", path: paths?.host?.profile?.location },
    { label: "Orders", path: paths?.host?.profile?.orders },
    { label: "History", path: paths?.host?.profile?.history },
    { label: "Requests", path: paths.host.profile.requests },
    { label: "Profile", path: paths?.host?.profile?.settings },
  ];

  // Main navigation items for mobile menu
  const mainNavItems = [
    { label: "Home", path: "/" },
    { label: "Search", path: "/search" },
    { label: "Orders", path: "/orders" },
    { label: "Chat", path: "/chat" },
  ];

  const isActive = (path: string) => pathname.startsWith(path);

  return (
    <>
      <nav className="fixed top-0 z-50 w-full bg-white/95 backdrop-blur-md border-b border-gray-200/50 dark:bg-gray-900/95 dark:border-gray-700/50 shadow-sm">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left Section: Tagline and Logo */}
            <div className="flex items-center">
              {/* Tagline - Only visible for guest users */}
              {user && !host && (
                <div className="hidden sm:block mr-4 lg:mr-6">
                  <span className="text-sm lg:text-base font-medium text-gray-600 dark:text-gray-300 whitespace-nowrap">
                    A place for friends and family to dine
                  </span>
                </div>
              )}

              <Link href="/" className="flex items-center">
                <img
                  src="/famfoody-logo.png"
                  alt="Eats Express Logo"
                  className="h-8 w-auto sm:h-10 object-contain"
                />
              </Link>
            </div>

            {/* Desktop Navigation Links (Host Mode) */}
            {host && (
              <div className="hidden lg:flex items-center space-x-8">
                {hostNavLinks.map((link) => (
                  <Link
                    key={link.label}
                    href={link.path}
                    className={`relative px-3 py-2 text-sm font-medium transition-all duration-200 rounded-md ${
                      isActive(link.path)
                        ? "text-green-600 bg-green-50"
                        : "text-gray-600 hover:text-green-600 hover:bg-gray-50"
                    }`}
                  >
                    {link.label}
                    {isActive(link.path) && (
                      <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-600 rounded-full"></span>
                    )}
                  </Link>
                ))}
              </div>
            )}

            {/* Right Section: Fixed Actions */}
            <div className="flex items-center space-x-2">
              {/* Desktop Actions - Hidden on mobile */}
              <div className="hidden lg:flex items-center space-x-3">
                {/* Role Switch Button */}
                <button
                  onClick={() =>
                    handleRoleChange(user, host, switchRole, router)
                  }
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 hover:border-green-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                  {host
                    ? "Switch to Guest"
                    : user
                    ? "Switch to Host"
                    : "Sign In"}
                </button>

                {/* User Menu */}
              </div>

              {/* Fixed Action Icons - Always Visible */}
              <div className="flex items-center space-x-1">
                {/* Cart - Always visible for non-hosts */}
                {!host && (
                  <Link
                    href="/cart"
                    className="relative p-2.5 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 touch-manipulation"
                    aria-label={`Cart (${cartItems.length} items)`}
                  >
                    <ShoppingBag className="w-6 h-6" />
                    {cartItems.length > 0 && (
                      <span className="absolute -top-1 -right-1 flex items-center justify-center min-w-[1.25rem] h-5 px-1 text-xs font-bold text-white bg-red-500 rounded-full animate-pulse">
                        {cartItems.length > 99 ? "99+" : cartItems.length}
                      </span>
                    )}
                  </Link>
                )}

                {/* Chat - Always visible when authenticated */}
                {authenticated && (
                  <Link
                    href="/chat"
                    className="relative p-2.5 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 touch-manipulation"
                    aria-label="Chat messages"
                  >
                    <MessageSquare className="w-6 h-6" />
                    <ChatBadge />
                  </Link>
                )}

                {/* Notifications - Desktop only */}
                {authenticated && (
                  <div className="">
                    <NotificationPopover />
                  </div>
                )}

                <div className="relative hidden lg:block ">
                  <button
                    data-user-menu-button
                    onClick={userMenu.onToggle}
                    className="flex items-center space-x-2 px-3 py-2 bg-gray-50 border border-gray-200 rounded-full hover:bg-gray-100 hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  >
                    {authenticated && (
                      <span className="text-sm font-medium text-green-600">
                        {getInitials(user?.name ?? host?.title)}
                      </span>
                    )}
                    <Image
                      width={32}
                      height={32}
                      src={user?.profilePicture ?? "/navbar/account.png"}
                      alt="User avatar"
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  </button>

                  {/* User Dropdown Menu */}
                  {userMenu.value && (user?.name || host?._id) && (
                    <div
                      data-user-menu
                      className="absolute right-0 top-14 w-64 bg-white border border-gray-200 rounded-xl shadow-lg dark:bg-gray-800 dark:border-gray-700 z-50 overflow-hidden"
                      style={{
                        animation: "slideInFromTop 0.2s ease-out forwards",
                      }}
                    >
                      <div className="p-4 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                        <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                          {user?.name ?? host?.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                          {user?.email
                            ? user?.email
                            : host?.address?.city
                            ? `${host?.address?.city}, ${host?.address?.state}`
                            : "Host Account"}
                        </p>
                      </div>

                      <div className="py-2">
                        {menuItems.map(({ label, path }) => (
                          <Link
                            key={label}
                            href={path}
                            onClick={userMenu.onFalse}
                            className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200"
                          >
                            {label}
                          </Link>
                        ))}
                      </div>

                      <div className="py-2 border-t border-gray-100 dark:border-gray-700">
                        <button
                          onClick={() => {
                            logout();
                            userMenu.onFalse();
                          }}
                          className="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-gray-700 transition-all duration-200"
                        >
                          Sign out
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Hamburger Menu Button */}
                <button
                  data-mobile-menu-button
                  onClick={mobileMenu.onToggle}
                  type="button"
                  className="inline-flex items-center justify-center p-2.5 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg lg:hidden transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 touch-manipulation"
                  aria-expanded={mobileMenu.value}
                  aria-label="Toggle navigation menu"
                >
                  <div className="relative w-6 h-6">
                    <Menu
                      className={`absolute inset-0 w-6 h-6 transition-all duration-300 ease-in-out ${
                        mobileMenu.value
                          ? "opacity-0 rotate-45 scale-75"
                          : "opacity-100 rotate-0 scale-100"
                      }`}
                    />
                    <X
                      className={`absolute inset-0 w-6 h-6 transition-all duration-300 ease-in-out ${
                        mobileMenu.value
                          ? "opacity-100 rotate-0 scale-100"
                          : "opacity-0 -rotate-45 scale-75"
                      }`}
                    />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {mobileMenu.value && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden transition-all duration-300 ease-out"
          onClick={mobileMenu.onFalse}
          aria-hidden="true"
          style={{
            animation: mobileMenu.value
              ? "fadeIn 0.3s ease-out"
              : "fadeOut 0.3s ease-out",
          }}
        />
      )}

      {/* Full-Screen Mobile Navigation Panel */}
      <div
        data-mobile-menu
        className={`fixed inset-0 z-50 bg-white dark:bg-gray-900 lg:hidden transition-all duration-500 ease-out mobile-menu-scroll mt-[64px] ${
          mobileMenu.value
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-full pointer-events-none"
        }`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="mobile-menu-title"
      >
        <div className="flex flex-col h-full">
          {/* Mobile Menu Header */}

          {/* Mobile Menu Content */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 mobile-menu-scroll">
            {/* Host Dashboard (Mobile) */}
            {host && (
              <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                  Host Dashboard
                </h3>
                <div className="space-y-2">
                  {hostNavLinks.map((link, index) => (
                    <Link
                      key={link.label}
                      href={link.path}
                      onClick={mobileMenu.onFalse}
                      className={`group flex items-center px-4 py-3 rounded-lg transition-all duration-200 touch-manipulation ${
                        isActive(link.path)
                          ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                          : "text-gray-600 hover:bg-green-50 hover:text-green-600 dark:text-gray-400 dark:hover:bg-green-900/20 dark:hover:text-green-400"
                      }`}
                      style={{
                        animationDelay: `${index * 75}ms`,
                      }}
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-200">
                        {link.label}
                      </span>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Account Section */}
            {authenticated && (
              <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                  Account & Settings
                </h3>
                <div className="space-y-2">
                  {menuItems.map(({ label, path }, index) => (
                    <Link
                      key={label}
                      href={path}
                      onClick={mobileMenu.onFalse}
                      className="group flex items-center px-4 py-3 text-gray-600 hover:bg-green-50 hover:text-green-600 dark:text-gray-400 dark:hover:bg-green-900/20 dark:hover:text-green-400 rounded-lg transition-all duration-200 touch-manipulation"
                      style={{
                        animationDelay: `${index * 75}ms`,
                      }}
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-200">
                        {label}
                      </span>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Role Switch Section */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => {
                  handleRoleChange(user, host, switchRole, router);
                  mobileMenu.onFalse();
                }}
                className="w-full flex items-center justify-center px-6 py-4 text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 font-medium focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 touch-manipulation mobile-nav-focus"
              >
                <User className="w-5 h-5 mr-3" />
                {host
                  ? "Switch to Guest Mode"
                  : user
                  ? "Switch to Host Mode"
                  : "Sign In to Continue"}
              </button>
            </div>
          </div>

          {/* Mobile Menu Footer */}
          {authenticated && (
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
              <div className="flex items-center space-x-4 mb-4">
                <div className="relative">
                  <Image
                    width={56}
                    height={56}
                    src={user?.profilePicture ?? "/navbar/account.png"}
                    alt="User avatar"
                    className="w-14 h-14 rounded-full object-cover ring-2 ring-green-200 dark:ring-green-700"
                  />
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white dark:border-gray-900"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-base font-semibold text-gray-900 dark:text-white truncate">
                    {user?.name ?? host?.title}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    {user?.email ?? "Host Account"}
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  logout();
                  mobileMenu.onFalse();
                }}
                className="w-full px-4 py-3 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 touch-manipulation"
              >
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
